import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PostManagementApi } from '#/api/post-management';

import { h } from 'vue';

import { Button, Space, Switch, Tag } from 'ant-design-vue';

// 推文状态配置
export const POST_STATUS_CONFIG = {
  draft: { color: 'default', text: '草稿' },
  pending: { color: 'processing', text: '待审核' },
  published: { color: 'success', text: '已发布' },
  archived: { color: 'warning', text: '已归档' },
};

// 推文状态选项
export const POST_STATUS_OPTIONS = [
  { label: '全部', value: '' },
  { label: '草稿', value: 'draft' },
  { label: '待审核', value: 'pending' },
  { label: '已发布', value: 'published' },
  { label: '已归档', value: 'archived' },
];

// 表格列配置
export function useColumns(onActionClick: (params: any) => void): VxeGridProps['columns'] {
  return [
    {
      type: 'checkbox',
      width: 50,
    },
    {
      title: 'ID',
      field: 'id',
      width: 80,
      sortable: true,
    },
    {
      title: '标题',
      field: 'title',
      minWidth: 200,
      showOverflow: 'tooltip',
    },
    {
      title: '唯一标识',
      field: 'slug',
      minWidth: 150,
      showOverflow: 'tooltip',
    },

    {
      title: '状态',
      field: 'status',
      minWidth: 100,
      slots: {
        default: ({ row }: { row: PostManagementApi.Post }) => {
          const config = POST_STATUS_CONFIG[row.status];
          return h(Tag, { color: config.color }, () => config.text);
        },
      },
    },
    {
      title: '上线状态',
      field: 'isOnline',
      minWidth: 100,
      slots: {
        default: ({ row }: { row: PostManagementApi.Post }) => {
          return h(Switch, {
            checked: row.isOnline,
            size: 'small',
            disabled: true,
          });
        },
      },
    },
    {
      title: '操作人',
      field: 'author',
      minWidth: 100,
      showOverflow: 'tooltip',
      slots: {
        default: ({ row }: { row: PostManagementApi.Post }) => {
          return row.author || h('span', { style: { color: '#999' } }, '未知');
        },
      },
    },
    {
      title: '阅读量',
      field: 'viewCount',
      width: 100,
      sortable: true,
      slots: {
        default: ({ row }: { row: PostManagementApi.Post }) => {
          return h('span', { style: { color: '#1890ff' } }, row.viewCount.toLocaleString());
        },
      },
    },
    {
      title: '创建时间',
      field: 'createdAt',
      width: 160,
      sortable: true,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return new Date(cellValue).toLocaleString();
      },
    },
    {
      title: '操作',
      field: 'actions',
      width: 200,
      fixed: 'right',
      slots: {
        default: ({ row }: { row: PostManagementApi.Post }) => {
          return h(Space, { size: 'small' }, () => [
            h(Button, {
              type: 'link',
              size: 'small',
              onClick: () => onActionClick({ action: 'edit', row }),
            }, () => '编辑'),

            h(Button, {
              type: 'link',
              size: 'small',
              danger: true,
              onClick: () => onActionClick({ action: 'delete', row }),
            }, () => '删除'),

            row.status === 'published' ?
              h(Button, {
                type: 'link',
                size: 'small',
                danger: true,
                onClick: () => onActionClick({ action: 'unpublish', row }),
              }, () => '下线') :
              h(Button, {
                type: 'link',
                size: 'small',
                onClick: () => onActionClick({ action: 'publish', row }),
              }, () => '发布'),
          ]);
        },
      },
    },
  ];
}

// 搜索表单配置
export function useGridFormSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入推文标题或内容关键词',
        clearable: true,
      },
      fieldName: 'search',
      label: '关键词搜索',
    },
    {
      component: 'Select',
      componentProps: {
        options: POST_STATUS_OPTIONS,
        placeholder: '请选择状态',
        clearable: true,
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作人名称',
        clearable: true,
      },
      fieldName: 'author',
      label: '操作人',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: '' },
          { label: '已上线', value: 'true' },
          { label: '未上线', value: 'false' },
        ],
        placeholder: '请选择上线状态',
        clearable: true,
      },
      fieldName: 'isOnline',
      label: '上线状态',
    },
    {
      component: 'RangePicker',
      componentProps: {
        placeholder: ['开始日期', '结束日期'],
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'dateRange',
      label: '创建时间',
    },
  ];
}

// 推文表单配置
export function useFormSchema() {
  return [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入推文标题',
        maxlength: 255,
        showCount: true,
      },
      fieldName: 'title',
      label: '推文标题',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入唯一标识（用于URL）',
        maxlength: 100,
      },
      fieldName: 'slug',
      label: '唯一标识',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入操作人名称',
      },
      fieldName: 'author',
      label: '操作人',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '草稿', value: 'draft' },
          { label: '待审核', value: 'pending' },
          { label: '已发布', value: 'published' },
        ],
        placeholder: '请选择状态',
      },
      fieldName: 'status',
      label: '状态',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        checkedChildren: '已上线',
        unCheckedChildren: '未上线',
      },
      fieldName: 'isOnline',
      label: '是否上线',
    },
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择发布时间',
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'publishDate',
      label: '发布时间',
    },
  ];
}
