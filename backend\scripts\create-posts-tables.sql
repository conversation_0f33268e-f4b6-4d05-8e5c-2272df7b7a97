-- 创建推文管理相关数据表

-- 1. 推文主表
CREATE TABLE IF NOT EXISTS `posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推文ID',
  `title` varchar(255) NOT NULL COMMENT '推文标题',
  `slug` varchar(100) NOT NULL COMMENT '唯一标识（用于URL）',
  `content` longtext NOT NULL COMMENT '推文内容',
  `author` varchar(100) DEFAULT NULL COMMENT '作者/操作人',
  `status` enum('draft','pending','published','archived') NOT NULL DEFAULT 'draft' COMMENT '状态：草稿、待审核、已发布、已归档',
  `is_online` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否上线：0-未上线，1-已上线',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '阅读量',
  `publish_date` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_slug` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_publish_date` (`publish_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推文表';

-- 2. 推文标签定义表
CREATE TABLE IF NOT EXISTS `post_tag_definitions` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `usage_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推文标签定义表';

-- 3. 推文标签关联表
CREATE TABLE IF NOT EXISTS `post_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `post_id` int(11) NOT NULL COMMENT '推文ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_tag` (`post_id`, `tag_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_tag_id` (`tag_id`),
  CONSTRAINT `fk_post_tags_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_post_tags_tag` FOREIGN KEY (`tag_id`) REFERENCES `post_tag_definitions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推文标签关联表';

-- 插入一些示例数据
INSERT INTO `posts` (`title`, `slug`, `content`, `author`, `status`, `is_online`, `view_count`, `publish_date`) VALUES
('用户协议', 'user-agreement', '<h1>用户协议</h1><p>欢迎使用我们的服务...</p>', '系统管理员', 'published', 1, 156, '2024-07-30 12:23:19'),
('隐私政策', 'privacy-policy', '<h1>隐私政策</h1><p>我们重视您的隐私...</p>', '系统管理员', 'published', 1, 89, '2024-07-30 13:14:47'),
('服务条款', 'terms-of-service', '<h1>服务条款</h1><p>使用本服务即表示您同意...</p>', '系统管理员', 'published', 1, 234, '2024-07-30 15:52:12'),
('常见问题', 'faq', '<h1>常见问题</h1><p>以下是用户经常询问的问题...</p>', '客服', 'published', 1, 67, '2024-07-30 16:18:36'),
('产品介绍', 'product-intro', '<h1>产品介绍</h1><p>我们的产品具有以下特点...</p>', '产品经理', 'draft', 0, 0, NULL),
('更新日志', 'changelog', '<h1>更新日志</h1><p>版本 1.0.0 更新内容...</p>', '开发团队', 'pending', 0, 12, NULL),
('使用指南', 'user-guide', '<h1>使用指南</h1><p>本指南将帮助您快速上手...</p>', '技术支持', 'published', 1, 145, '2024-07-30 14:25:30'),
('联系我们', 'contact-us', '<h1>联系我们</h1><p>如有任何问题，请通过以下方式联系我们...</p>', '客服', 'published', 1, 78, '2024-07-30 17:08:15');

-- 插入标签定义
INSERT INTO `post_tag_definitions` (`name`, `usage_count`) VALUES
('法律文档', 3),
('用户帮助', 2),
('产品相关', 2),
('技术支持', 1);

-- 插入标签关联
INSERT INTO `post_tags` (`post_id`, `tag_id`) VALUES
(1, 1), -- 用户协议 - 法律文档
(2, 1), -- 隐私政策 - 法律文档  
(3, 1), -- 服务条款 - 法律文档
(4, 2), -- 常见问题 - 用户帮助
(5, 3), -- 产品介绍 - 产品相关
(6, 3), -- 更新日志 - 产品相关
(7, 2), -- 使用指南 - 用户帮助
(7, 4), -- 使用指南 - 技术支持
(8, 2); -- 联系我们 - 用户帮助
