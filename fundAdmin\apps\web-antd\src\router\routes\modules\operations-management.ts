import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'carbon:settings-services',
      order: 1.5, // 在用户运营(1)和基金运营(2)之间
      title: '运营管理',
    },
    name: 'OperationsManagement',
    path: '/operations-management',
    children: [
      {
        path: '/operations-management/banners',
        name: 'BannerManagement',
        meta: {
          icon: 'carbon:image',
          title: 'Banner管理',
        },
        component: () => import('#/views/banner-management/index.vue'),
      },
      {
        path: '/operations-management/news',
        name: 'NewsManagement',
        meta: {
          icon: 'carbon:document',
          title: '新闻管理',
        },
        component: () => import('#/views/news-management/index.vue'),
      },
      {
        path: '/operations-management/posts',
        name: 'PostManagement',
        meta: {
          icon: 'carbon:chat',
          title: '推文管理',
        },
        component: () => import('#/views/post-management/index.vue'),
      },
    ],
  },
  {
    meta: {
      icon: 'carbon:shopping-cart',
      order: 1.6, // 在运营管理(1.5)和剧目运营(1.7)之间
      title: '订单管理',
    },
    name: 'OrderManagement',
    path: '/order-management',
    children: [
      {
        path: '/order-management/recharge-records',
        name: 'RechargeRecordsManagement',
        meta: {
          icon: 'carbon:wallet',
          title: '充值记录',
        },
        component: () => import('#/views/order-management/recharge-records/index.vue'),
      },
      {
        path: '/order-management/investment-records',
        name: 'InvestmentRecordsManagement',
        meta: {
          icon: 'carbon:money',
          title: '投资记录',
        },
        component: () => import('#/views/order-management/investment-records/index.vue'),
      },
    ],
  },
];

export default routes;
