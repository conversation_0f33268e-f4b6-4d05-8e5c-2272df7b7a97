<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue';

import { useVbenForm, useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { message, Button } from 'ant-design-vue';

import QuillEditor from '#/components/QuillEditor/index.vue';

import {
  createPost,
  updatePost,
  type PostManagementApi,
} from '#/api/post-management';

import { useFormSchema } from '../data';

interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  success: [];
}>();

// 状态
const id = ref<number | undefined>();
const content = ref('');

// Quill编辑器引用
const quillEditorRef = ref();


// 抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) {
      message.error('请填写必填字段');
      return;
    }

    // 推文内容不是必填项，可以为空

    const values = await formApi.getValues();
    drawerApi.lock();

    // 构建提交数据
    const submitData: PostManagementApi.CreatePostParams = {
      ...values,
      content: content.value,
    };

    try {
      if (id.value) {
        await updatePost(id.value, submitData);
        message.success('推文更新成功');
      } else {
        await createPost(submitData);
        message.success('推文创建成功');
      }

      emits('success');
      drawerApi.close();
      props.onSuccess?.();
    } catch (error: any) {
      message.error(error.message || '操作失败');
    } finally {
      drawerApi.unlock();
    }
  },
  onOpenChange: (isOpen: boolean) => {
    if (isOpen) {
      // 重置表单
      formApi.resetForm();

      // 重置状态
      id.value = undefined;
      content.value = '';

      // 获取传入的数据
      const data = drawerApi.getData<any>();
      console.log('Drawer获取的数据:', data);
      console.log('数据类型:', typeof data);
      console.log('数据是否为空对象:', Object.keys(data || {}).length === 0);

      if (data && Object.keys(data).length > 0) {
        console.log('编辑模式，设置数据:', data);
        id.value = data.id;

        // 设置富文本编辑器内容
        const contentValue = data.content || '';
        console.log('设置编辑器内容长度:', contentValue.length);
        content.value = contentValue;

        // 设置表单数据
        formApi.setValues({
          title: data.title,
          slug: data.slug,
          author: data.author,
          status: data.status,
          isOnline: data.isOnline,
          publishDate: data.publishDate,
        });

        // 延迟更新富文本编辑器，确保组件已渲染
        setTimeout(() => {
          if (quillEditorRef.value && contentValue) {
            console.log('延迟设置编辑器内容');
            quillEditorRef.value.setHTML(contentValue);
          }
        }, 100);
      } else {
        console.log('新增模式');
      }
    }
  },
});

// 表单
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

// 计算标题
const title = computed(() => {
  return id.value ? '编辑推文' : '新增推文';
});


// 暴露方法给父组件
defineExpose({
  open: drawerApi.open,
  setData: drawerApi.setData,
});

// 移除onMounted中的loadTags调用，只在抽屉打开时加载
</script>

<template>
  <Drawer :title="title">
    <Form />
    

    
    <!-- 内容编辑器 -->
    <div class="mb-4">
      <label class="block text-sm font-medium mb-2">推文内容</label>
      <QuillEditor
        ref="quillEditorRef"
        v-model:value="content"
        placeholder="请输入推文内容..."
        :height="300"
      />
    </div>
    

  </Drawer>
</template>
