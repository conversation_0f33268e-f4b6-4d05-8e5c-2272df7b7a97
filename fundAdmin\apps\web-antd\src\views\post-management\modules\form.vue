<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue';

import { useVbenForm, useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { message, Select, Tag, Input, Button } from 'ant-design-vue';

import QuillEditor from '#/components/QuillEditor/index.vue';

import {
  createPost,
  updatePost,
  getAllTags,
  createTag,
  type PostManagementApi,
} from '#/api/post-management';

import { useFormSchema } from '../data';

interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  success: [];
}>();

// 抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  title: computed(() => (isEdit.value ? '编辑推文' : '新增推文')),
  loading: submitLoading,
  onOpenChange: (isOpen: boolean) => {
    if (isOpen) {
      resetForm();
      loadTags();
    }
  },
});

// 表单
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

// 状态
const isEdit = ref(false);
const submitLoading = ref(false);
const editId = ref<number>();
const content = ref('');
const availableTags = ref<PostManagementApi.PostTag[]>([]);
const selectedTags = ref<string[]>([]);
const newTagName = ref('');

// 加载标签
async function loadTags() {
  try {
    const response = await getAllTags();
    availableTags.value = response.data;
  } catch (error) {
    console.error('加载标签失败:', error);
  }
}

// 重置表单
function resetForm() {
  formApi.resetForm();
  content.value = '';
  selectedTags.value = [];
  newTagName.value = '';
  isEdit.value = false;
  editId.value = undefined;
}

// 设置编辑数据
function setEditData(data: any) {
  isEdit.value = true;
  editId.value = data.id;
  content.value = data.content || '';
  selectedTags.value = data.tags || [];
  
  // 设置表单数据
  formApi.setValues({
    title: data.title,
    slug: data.slug,
    author: data.author,
    status: data.status,
    isOnline: data.isOnline,
    publishDate: data.publishDate,
  });
}

// 添加新标签
async function addNewTag() {
  if (!newTagName.value.trim()) {
    message.warning('请输入标签名称');
    return;
  }
  
  try {
    await createTag(newTagName.value.trim());
    message.success('标签创建成功');
    newTagName.value = '';
    await loadTags();
  } catch (error: any) {
    message.error(error.message || '标签创建失败');
  }
}

// 提交表单
async function handleSubmit() {
  try {
    submitLoading.value = true;
    
    // 验证表单
    const formData = await formApi.validate();
    
    // 验证内容
    if (!content.value.trim()) {
      message.error('请输入推文内容');
      return;
    }
    
    // 构建提交数据
    const submitData: PostManagementApi.CreatePostParams = {
      ...formData,
      content: content.value,
      tags: selectedTags.value,
    };
    
    if (isEdit.value && editId.value) {
      await updatePost(editId.value, submitData);
      message.success('推文更新成功');
    } else {
      await createPost(submitData);
      message.success('推文创建成功');
    }
    
    drawerApi.close();
    emits('success');
    props.onSuccess?.();
  } catch (error: any) {
    message.error(error.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
}

// 暴露方法给父组件
defineExpose({
  open: drawerApi.open,
  close: drawerApi.close,
  setData: setEditData,
});

onMounted(() => {
  loadTags();
});
</script>

<template>
  <Drawer>
    <Form />
    
    <!-- 标签选择 -->
    <div class="mb-4">
      <label class="block text-sm font-medium mb-2">推文标签</label>
      <div class="space-y-2">
        <Select
          v-model:value="selectedTags"
          mode="multiple"
          placeholder="请选择标签"
          :options="availableTags.map(tag => ({ label: tag.name, value: tag.name }))"
          class="w-full"
        />
        
        <!-- 添加新标签 -->
        <div class="flex gap-2">
          <Input
            v-model:value="newTagName"
            placeholder="输入新标签名称"
            @press-enter="addNewTag"
          />
          <Button @click="addNewTag">添加标签</Button>
        </div>
        
        <!-- 已选标签显示 -->
        <div v-if="selectedTags.length > 0" class="flex flex-wrap gap-1">
          <Tag
            v-for="tag in selectedTags"
            :key="tag"
            closable
            @close="selectedTags = selectedTags.filter(t => t !== tag)"
          >
            {{ tag }}
          </Tag>
        </div>
      </div>
    </div>
    
    <!-- 内容编辑器 -->
    <div class="mb-4">
      <label class="block text-sm font-medium mb-2">推文内容 *</label>
      <QuillEditor
        v-model:value="content"
        placeholder="请输入推文内容..."
        :height="300"
      />
    </div>
    
    <!-- 操作按钮 -->
    <div class="flex justify-end gap-2 pt-4 border-t">
      <Button @click="drawerApi.close">取消</Button>
      <Button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ isEdit ? '更新' : '创建' }}
      </Button>
    </div>
  </Drawer>
</template>
