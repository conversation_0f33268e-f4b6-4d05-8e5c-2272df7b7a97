<script lang="ts" setup>
import { computed, ref, watch, onMounted } from 'vue';

import { useVbenForm, useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { message, Button } from 'ant-design-vue';

import QuillEditor from '#/components/QuillEditor/index.vue';

import {
  createPost,
  updatePost,
  type PostManagementApi,
} from '#/api/post-management';

import { useFormSchema } from '../data';

interface Props {
  onSuccess?: () => void;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  success: [];
}>();

// 状态
const isEdit = ref(false);
const submitLoading = ref(false);
const editId = ref<number>();
const content = ref('');


// 抽屉
const [Drawer, drawerApi] = useVbenDrawer({
  title: computed(() => (isEdit.value ? '编辑推文' : '新增推文')),
  loading: submitLoading,
  onOpenChange: (isOpen: boolean) => {
    // 不在这里重置表单，由具体的打开方法决定
  },
});

// 表单
const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});



// 重置表单
function resetForm() {
  formApi.resetForm();
  content.value = '';
  isEdit.value = false;
  editId.value = undefined;
}

// 设置编辑数据
function setEditData(data: any) {
  isEdit.value = true;
  editId.value = data.id;
  content.value = data.content || '';

  // 设置表单数据
  formApi.setValues({
    title: data.title,
    slug: data.slug,
    author: data.author,
    status: data.status,
    isOnline: data.isOnline,
    publishDate: data.publishDate,
  });
}



// 提交表单
async function handleSubmit() {
  try {
    submitLoading.value = true;
    
    // 验证表单
    const formData = await formApi.validate();
    
    // 验证内容
    if (!content.value.trim()) {
      message.error('请输入推文内容');
      return;
    }
    
    // 构建提交数据
    const submitData: PostManagementApi.CreatePostParams = {
      ...formData,
      content: content.value,
    };
    
    if (isEdit.value && editId.value) {
      await updatePost(editId.value, submitData);
      message.success('推文更新成功');
    } else {
      await createPost(submitData);
      message.success('推文创建成功');
    }
    
    drawerApi.close();
    emits('success');
    props.onSuccess?.();
  } catch (error: any) {
    message.error(error.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
}

// 打开新增抽屉
function openForCreate() {
  resetForm();
  drawerApi.open();
}

// 打开编辑抽屉
function openForEdit(data: any) {
  setEditData(data);
  drawerApi.open();
}

// 暴露方法给父组件
defineExpose({
  open: openForCreate,
  close: drawerApi.close,
  setData: openForEdit,
});

// 移除onMounted中的loadTags调用，只在抽屉打开时加载
</script>

<template>
  <Drawer>
    <Form />
    

    
    <!-- 内容编辑器 -->
    <div class="mb-4">
      <label class="block text-sm font-medium mb-2">推文内容 *</label>
      <QuillEditor
        v-model:value="content"
        placeholder="请输入推文内容..."
        :height="300"
      />
    </div>
    

  </Drawer>
</template>
