import { readBody } from 'h3'
import { executeQuery } from '~/utils/database'

/**
 * 创建推文标签接口
 * POST /api/admin/posts/tags
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建标签'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { name } = body;

    // 验证标签名称
    if (!name || !name.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: '标签名称不能为空'
      });
    }

    const trimmedName = name.trim();

    // 检查标签是否已存在
    const existingTag = await executeQuery(
      'SELECT id FROM post_tag_definitions WHERE name = ?',
      [trimmedName]
    );

    if (existingTag.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '标签已存在'
      });
    }

    // 创建标签
    const result = await executeQuery(
      'INSERT INTO post_tag_definitions (name, usage_count, created_at) VALUES (?, 0, NOW())',
      [trimmedName]
    );

    // 记录操作日志
    // await logAdminAction(admin.id, 'CREATE_TAG', `创建标签: ${trimmedName}`, { tagId: result.insertId });

    return {
      success: true,
      message: '标签创建成功',
      data: {
        id: result.insertId,
        name: trimmedName,
        usageCount: 0
      }
    };

  } catch (error: any) {
    console.error('创建标签失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '创建标签失败'
    });
  }
});
