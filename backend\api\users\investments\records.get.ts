/**
 * 获取用户投资记录接口
 * GET /api/users/investments/records
 */

import { query } from '~/utils/database'
import { verifyUserAccessToken } from '~/utils/auth'

export default defineEventHandler(async (event) => {
  try {
    console.log('Investment records API called');

    // 验证用户认证
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '用户认证失败，请重新登录'
      });
    }

    const userId = userPayload.id;

    // 获取查询参数
    const queryParams = getQuery(event);
    const page = parseInt(queryParams.page as string) || 1;
    const pageSize = parseInt(queryParams.pageSize as string) || 10;
    const offset = (page - 1) * pageSize;

    // 查询投资记录总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_investments
      WHERE user_id = ?
    `;
    const countResult = await query(countQuery, [userId]);
    const total = countResult[0].total;

    // 查询投资记录详情（关联交易记录表获取真实的投资编号）
    const recordsQuery = `
      SELECT
        ui.id,
        ui.project_id,
        ui.project_name,
        ui.investment_amount,
        ui.expected_return_rate,
        ui.expected_return_amount,
        COALESCE(ui.actual_return_amount, 0) as actual_return_amount,
        DATE_FORMAT(ui.investment_date, '%Y-%m-%d %H:%i') as investment_date,
        DATE_FORMAT(ui.start_date, '%Y-%m-%d') as start_date,
        DATE_FORMAT(ui.end_date, '%Y-%m-%d') as end_date,
        CASE
          WHEN ui.project_status = 'active' THEN '进行中'
          WHEN ui.project_status = 'completed' THEN '已完成'
          WHEN ui.project_status = 'paused' THEN '暂停'
          ELSE '未知'
        END as project_status,
        CASE
          WHEN ui.investment_status = 'active' THEN '有效'
          WHEN ui.investment_status = 'completed' THEN '已完成'
          WHEN ui.investment_status = 'cancelled' THEN '已取消'
          ELSE '未知'
        END as investment_status,
        ui.progress,
        DATEDIFF(ui.end_date, NOW()) as remaining_days,
        COALESCE(uat.transaction_no, CONCAT('INV', LPAD(ui.id, 6, '0'))) as investment_no
      FROM user_investments ui
      LEFT JOIN user_asset_transactions uat ON (
        uat.related_type = 'investment'
        AND uat.related_id = ui.id
        AND uat.transaction_type = 'shells_out'
        AND uat.user_id = ui.user_id
      )
      WHERE ui.user_id = ?
      ORDER BY ui.investment_date DESC
      LIMIT ? OFFSET ?
    `;

    const recordsRows = await query(recordsQuery, [userId, pageSize, offset]);

    // 处理投资记录数据
    const records = recordsRows.map(record => ({
      id: record.id,
      projectId: record.project_id,
      projectName: record.project_name,
      investmentAmount: parseFloat(record.investment_amount),
      expectedReturnRate: parseFloat(record.expected_return_rate),
      expectedReturnAmount: parseFloat(record.expected_return_amount),
      actualReturnAmount: parseFloat(record.actual_return_amount),
      investmentDate: record.investment_date,
      startDate: record.start_date,
      endDate: record.end_date,
      projectStatus: record.project_status,
      investmentStatus: record.investment_status,
      progress: parseFloat(record.progress),
      remainingDays: record.remaining_days > 0 ? record.remaining_days : 0,
      // 计算收益率
      returnRate: record.investment_amount > 0 ?
        parseFloat(((record.actual_return_amount / record.investment_amount) * 100).toFixed(2)) : 0,
      // 使用真实的投资编号（从交易记录表获取）
      investmentNo: record.investment_no
    }));

    return {
      success: true,
      data: {
        records,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };

  } catch (error) {
    console.error('获取投资记录失败:', error);
    return {
      success: false,
      message: '获取投资记录失败',
      error: error.message
    };
  }
});
