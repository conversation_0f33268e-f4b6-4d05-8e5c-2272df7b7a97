// 推文管理相关类型定义

export namespace PostManagementApi {
  // 推文状态枚举
  export type PostStatus = 'draft' | 'pending' | 'published' | 'archived';

  // 推文基本信息
  export interface Post {
    id: number;
    title: string;
    slug: string;
    content: string;
    author?: string;
    status: PostStatus;
    isOnline: boolean;
    viewCount: number;
    publishDate?: string;
    createdAt: string;
    updatedAt: string;
  }

  // 推文列表查询参数
  export interface PostListParams {
    page?: number;
    pageSize?: number;
    status?: PostStatus;
    search?: string;
    author?: string;
    isOnline?: boolean;
    startDate?: string;
    endDate?: string;
    orderBy?: string;
    orderDirection?: 'ASC' | 'DESC';
  }

  // 推文列表响应
  export interface PostListResponse {
    list: Post[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }

  // 推文详情响应
  export interface PostDetailResponse {
    data: Post;
  }

  // 创建推文参数
  export interface CreatePostParams {
    title: string;
    slug: string;
    content: string;
    author?: string;
    status?: PostStatus;
    isOnline?: boolean;
    publishDate?: string;
  }

  // 更新推文参数
  export interface UpdatePostParams extends Partial<CreatePostParams> {
    // 继承创建参数，所有字段都是可选的
  }

  // 发布推文参数
  export interface PublishPostParams {
    action: 'publish' | 'unpublish' | 'archive';
    publishDate?: string;
  }

  // 批量操作参数
  export interface BatchOperatePostParams {
    ids: number[];
    action: 'publish' | 'unpublish' | 'delete' | 'archive';
  }

  // 操作响应
  export interface OperationResponse {
    success: boolean;
    message: string;
    data?: any;
  }

  // 推文统计数据
  export interface PostStats {
    total: number;
    published: number;
    draft: number;
    pending: number;
    archived: number;
    totalViews: number;
    todayViews: number;
  }

  // 上传响应
  export interface UploadResponse {
    success: boolean;
    data: {
      url: string;
      filename: string;
      size: number;
    };
  }
}
