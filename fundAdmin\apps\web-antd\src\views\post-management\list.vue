<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref, onMounted, onUnmounted } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { $t } from '@vben/locales';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deletePost,
  getPostList,
  publishPost,
  batchOperatePost,
  type PostManagementApi,
} from '#/api/post-management';

import { useColumns, useGridFormSchema, POST_STATUS_CONFIG } from './data';
import Form from './modules/form.vue';

defineOptions({
  name: 'PostManagementList',
});

// 表单抽屉
const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  title: '推文管理',
});

// 表格配置
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    showOverflow: true,
    columnConfig: {
      resizable: true,
    },
    pagerConfig: {},
    rowConfig: {
      keyField: 'id',
    },
    cellConfig: {
      height: 60,
    },
    checkboxConfig: {
      reserve: true,
    },
    toolbarConfig: {
      custom: true,
      export: false,
      refresh: true,
      refreshOptions: { code: 'query' },
      search: true,
      zoom: true,
      zoomOptions: {},
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          // 过滤掉undefined和空值
          const params = Object.fromEntries(
            Object.entries(formValues || {}).filter(([_, value]) =>
              value !== undefined && value !== null && value !== ''
            )
          );

          // 处理日期范围
          if (params.dateRange && Array.isArray(params.dateRange)) {
            params.startDate = params.dateRange[0];
            params.endDate = params.dateRange[1];
            delete params.dateRange;
          }

          const response = await getPostList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...params,
          });

          return {
            result: response.list || [],
            total: response.pagination?.total || 0,
          };
        },
      },
      response: {
        result: 'result',
        total: 'total',
      },
    },
  } as VxeTableGridOptions,
});

// 新增推文
function onCreate() {
  // 明确清理数据，确保是新增模式
  formDrawerApi.setData(null).open();
}

// 编辑推文
function onEdit(row: PostManagementApi.Post) {
  console.log('点击编辑，原始数据:', row);
  const editData = {
    ...row,
  };
  console.log('传递给表单的编辑数据:', editData);
  formDrawerApi.setData(editData).open();
}

// 删除推文
function onDelete(row: PostManagementApi.Post) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除推文"${row.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        await deletePost(row.id);
        message.success('删除成功');
        gridApi.grid?.commitProxy('query');
      } catch (error: any) {
        message.error(error.message || '删除失败');
      }
    },
  });
}

// 发布/下线推文
async function onPublish(row: PostManagementApi.Post, action: 'publish' | 'unpublish') {
  try {
    const actionText = action === 'publish' ? '发布' : '下线';
    await publishPost(row.id, { action });
    message.success(`${actionText}成功`);
    gridApi.grid?.commitProxy('query');
  } catch (error: any) {
    message.error(error.message || '操作失败');
  }
}

// 操作点击处理
function onActionClick({ action, row }: OnActionClickParams) {
  switch (action) {
    case 'edit':
      onEdit(row);
      break;
    case 'delete':
      onDelete(row);
      break;
    case 'publish':
      onPublish(row, 'publish');
      break;
    case 'unpublish':
      onPublish(row, 'unpublish');
      break;
  }
}

// 批量操作
async function onBatchOperation(action: 'publish' | 'unpublish' | 'delete') {
  const selectedRows = gridApi.grid?.getCheckboxRecords() || [];
  if (selectedRows.length === 0) {
    message.warning('请先选择要操作的推文');
    return;
  }

  const actionText = {
    publish: '发布',
    unpublish: '下线',
    delete: '删除',
  }[action];

  Modal.confirm({
    title: `确认${actionText}`,
    content: `确定要${actionText}选中的 ${selectedRows.length} 条推文吗？`,
    okText: '确定',
    cancelText: '取消',
    okType: action === 'delete' ? 'danger' : 'primary',
    onOk: async () => {
      try {
        const ids = selectedRows.map((row: PostManagementApi.Post) => row.id);
        await batchOperatePost({ ids, action });
        message.success(`批量${actionText}成功`);
        gridApi.grid?.commitProxy('query');
        gridApi.grid?.clearCheckboxRow();
      } catch (error: any) {
        message.error(error.message || `批量${actionText}失败`);
      }
    },
  });
}

// 页面挂载时加载数据
onMounted(() => {
  gridApi.grid?.commitProxy('query');
});
</script>

<template>
  <Page auto-content-height>
    <FormDrawer :on-success="() => gridApi.grid?.commitProxy('query')" />
    <Grid :table-title="'推文管理'">
      <template #toolbar-tools>
        <div class="flex gap-2">
          <Button type="primary" @click="onCreate">
            <Plus class="size-5" />
            {{ $t('ui.actionTitle.create', ['推文']) }}
          </Button>
          <Button @click="onBatchOperation('publish')">
            批量发布
          </Button>
          <Button @click="onBatchOperation('unpublish')">
            批量下线
          </Button>
          <Button danger @click="onBatchOperation('delete')">
            批量删除
          </Button>
        </div>
      </template>
    </Grid>
  </Page>
</template>
