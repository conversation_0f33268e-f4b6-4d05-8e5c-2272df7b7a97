import { readBody, getRouterParam } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员发布/下线推文接口
 * POST /api/admin/posts/[id]/publish
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法操作推文'
    //   });
    // }

    // 获取推文ID
    const postId = getRouterParam(event, 'id');
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的推文ID'
      });
    }

    // 检查推文是否存在
    const existingPost = await query(
      'SELECT id, title, status FROM posts WHERE id = ?',
      [postId]
    );

    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '推文不存在'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { action, publishDate } = body;

    // 验证操作类型
    const validActions = ['publish', 'unpublish', 'archive'];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的操作类型'
      });
    }

    // 根据操作类型设置状态和上线状态
    let newStatus;
    let isOnline;
    let newPublishDate = publishDate;

    switch (action) {
      case 'publish':
        newStatus = 'published';
        isOnline = 1;
        if (!newPublishDate) {
          newPublishDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
        }
        break;
      case 'unpublish':
        newStatus = 'draft';
        isOnline = 0;
        newPublishDate = null;
        break;
      case 'archive':
        newStatus = 'archived';
        isOnline = 0;
        break;
    }

    // 更新推文状态
    const updateQuery = `
      UPDATE posts
      SET status = ?, is_online = ?, publish_date = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await query(updateQuery, [newStatus, isOnline, newPublishDate, postId]);

    // 记录操作日志
    const actionText = {
      publish: '发布',
      unpublish: '下线',
      archive: '归档'
    }[action];

    // await logAdminAction(admin.id, `${action.toUpperCase()}_POST`, `${actionText}推文: ${existingPost[0].title}`, { postId });

    return {
      success: true,
      message: `推文${actionText}成功`,
      data: {
        id: postId,
        action,
        status: newStatus,
        isOnline: Boolean(isOnline)
      }
    };

  } catch (error: any) {
    console.error('操作推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '操作推文失败'
    });
  }
});
