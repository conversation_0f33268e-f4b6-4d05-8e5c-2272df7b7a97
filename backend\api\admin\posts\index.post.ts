import { readBody } from 'h3'
import { executeQuery } from '~/utils/database'

/**
 * 管理员创建推文接口
 * POST /api/admin/posts
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建推文'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { 
      title, 
      slug,
      content, 
      author, 
      status = 'draft',
      isOnline = false,
      publishDate,
      tags = []
    } = body;

    // 验证必填字段
    if (!title || !slug || !content) {
      throw createError({
        statusCode: 400,
        statusMessage: '标题、唯一标识和内容为必填字段'
      });
    }

    // 验证slug唯一性
    const existingPost = await executeQuery(
      'SELECT id FROM posts WHERE slug = ?',
      [slug]
    );

    if (existingPost.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '唯一标识已存在，请使用其他标识'
      });
    }

    // 验证状态值
    const validStatuses = ['draft', 'pending', 'published', 'archived'];
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的状态值'
      });
    }

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      // 插入推文
      const insertPostQuery = `
        INSERT INTO posts (
          title, slug, content, author, status, is_online, 
          publish_date, view_count, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
      `;

      const postResult = await executeQuery(insertPostQuery, [
        title,
        slug,
        content,
        author || admin.username,
        status,
        isOnline ? 1 : 0,
        publishDate || null
      ]);

      const postId = postResult.insertId;

      // 处理标签
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          if (!tagName.trim()) continue;

          // 查找或创建标签
          let tagResult = await executeQuery(
            'SELECT id FROM post_tag_definitions WHERE name = ?',
            [tagName.trim()]
          );

          let tagId;
          if (tagResult.length === 0) {
            // 创建新标签
            const createTagResult = await executeQuery(
              'INSERT INTO post_tag_definitions (name, usage_count, created_at) VALUES (?, 1, NOW())',
              [tagName.trim()]
            );
            tagId = createTagResult.insertId;
          } else {
            tagId = tagResult[0].id;
            // 更新使用次数
            await executeQuery(
              'UPDATE post_tag_definitions SET usage_count = usage_count + 1 WHERE id = ?',
              [tagId]
            );
          }

          // 关联推文和标签
          await executeQuery(
            'INSERT INTO post_tags (post_id, tag_id) VALUES (?, ?)',
            [postId, tagId]
          );
        }
      }

      // 提交事务
      await executeQuery('COMMIT');

      // 记录操作日志
      // await logAdminAction(admin.id, 'CREATE_POST', `创建推文: ${title}`, { postId });

      return {
        success: true,
        message: '推文创建成功',
        data: {
          id: postId,
          title,
          slug
        }
      };

    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }

  } catch (error: any) {
    console.error('创建推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '创建推文失败'
    });
  }
});
