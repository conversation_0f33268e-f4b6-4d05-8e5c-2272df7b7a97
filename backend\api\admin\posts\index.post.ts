import { readBody } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员创建推文接口
 * POST /api/admin/posts
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法创建推文'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const {
      title,
      slug,
      content,
      author,
      status = 'draft',
      isOnline = false,
      publishDate
    } = body;

    // 验证必填字段
    if (!title || !slug || !content) {
      throw createError({
        statusCode: 400,
        statusMessage: '标题、唯一标识和内容为必填字段'
      });
    }

    // 验证slug唯一性
    const existingPost = await query(
      'SELECT id FROM posts WHERE slug = ?',
      [slug]
    );

    if (existingPost.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '唯一标识已存在，请使用其他标识'
      });
    }

    // 验证状态值
    const validStatuses = ['draft', 'pending', 'published', 'archived'];
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的状态值'
      });
    }

    // 插入推文
    const insertPostQuery = `
      INSERT INTO posts (
        title, slug, content, author, status, is_online,
        publish_date, view_count, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
    `;

    // 处理发布日期格式
    let formattedPublishDate = null;
    if (publishDate) {
      try {
        const date = new Date(publishDate);
        // 转换为MySQL datetime格式 (YYYY-MM-DD HH:mm:ss)
        formattedPublishDate = date.toISOString().slice(0, 19).replace('T', ' ');
      } catch (error) {
        console.error('日期格式转换失败:', error);
        formattedPublishDate = null;
      }
    }

    const postResult = await query(insertPostQuery, [
      title,
      slug,
      content,
      author || admin.username,
      status,
      isOnline ? 1 : 0,
      formattedPublishDate
    ]);

    const postId = postResult.insertId;

    // 记录操作日志
    // await logAdminAction(admin.id, 'CREATE_POST', `创建推文: ${title}`, { postId });

    return {
      success: true,
      message: '推文创建成功',
      data: {
        id: postId,
        title,
        slug
      }
    };

  } catch (error: any) {
    console.error('创建推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '创建推文失败'
    });
  }
});
