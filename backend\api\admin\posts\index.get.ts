import { getQuery } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员获取推文列表接口
 * GET /api/admin/posts
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看推文列表'
    //   });
    // }

    // 获取查询参数
    const query_params = getQuery(event);
    const { 
      page = 1, 
      pageSize = 20, 
      status,
      search,
      author,
      isOnline,
      startDate,
      endDate,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = query_params;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    // 上线状态筛选
    if (isOnline !== undefined && isOnline !== '') {
      whereConditions.push('is_online = ?');
      queryParams.push(isOnline === 'true' ? 1 : 0);
    }

    // 关键词搜索
    if (search) {
      whereConditions.push('(title LIKE ? OR content LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // 作者筛选
    if (author) {
      whereConditions.push('author LIKE ?');
      queryParams.push(`%${author}%`);
    }

    // 日期范围筛选
    if (startDate) {
      whereConditions.push('created_at >= ?');
      queryParams.push(startDate);
    }
    if (endDate) {
      whereConditions.push('created_at <= ?');
      queryParams.push(endDate + ' 23:59:59');
    }

    // 构建WHERE子句
    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 验证排序字段
    const allowedOrderFields = ['id', 'title', 'status', 'view_count', 'created_at', 'updated_at'];
    const safeOrderBy = allowedOrderFields.includes(orderBy as string) ? orderBy : 'created_at';
    const safeOrderDirection = orderDirection === 'ASC' ? 'ASC' : 'DESC';

    // 计算偏移量
    const offset = (Number(page) - 1) * Number(pageSize);

    // 查询推文列表
    const postsQuery = `
      SELECT p.*
      FROM posts p
      ${whereClause}
      ORDER BY p.${safeOrderBy} ${safeOrderDirection}
      LIMIT ? OFFSET ?
    `;

    const posts = await query(postsQuery, [...queryParams, Number(pageSize), offset]);

    // 查询总数
    const countQuery = `
      SELECT COUNT(p.id) as total
      FROM posts p
      ${whereClause}
    `;

    const countResult = await query(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // 处理推文数据
    const processedPosts = posts.map((post: any) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      author: post.author,
      status: post.status,
      isOnline: Boolean(post.is_online),
      viewCount: post.view_count || 0,
      publishDate: post.publish_date,
      createdAt: post.created_at,
      updatedAt: post.updated_at
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / Number(pageSize));

    return {
      success: true,
      data: {
        list: processedPosts,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages
        }
      }
    };

  } catch (error: any) {
    console.error('获取推文列表失败:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '获取推文列表失败'
    });
  }
});
