import { readBody } from 'h3'
import { executeQuery } from '~/utils/database'

/**
 * 管理员批量操作推文接口
 * POST /api/admin/posts/batch-operate
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法批量操作推文'
    //   });
    // }

    // 获取请求体
    const body = await readBody(event);
    const { ids, action } = body;

    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '请选择要操作的推文'
      });
    }

    // 验证操作类型
    const validActions = ['publish', 'unpublish', 'delete', 'archive'];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的操作类型'
      });
    }

    // 验证推文ID
    const validIds = ids.filter(id => !isNaN(Number(id)));
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的推文ID'
      });
    }

    // 检查推文是否存在
    const placeholders = validIds.map(() => '?').join(',');
    const existingPosts = await executeQuery(
      `SELECT id, title FROM posts WHERE id IN (${placeholders})`,
      validIds
    );

    if (existingPosts.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '未找到要操作的推文'
      });
    }

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      let affectedCount = 0;
      const actionText = {
        publish: '发布',
        unpublish: '下线',
        delete: '删除',
        archive: '归档'
      }[action];

      if (action === 'delete') {
        // 批量删除操作
        for (const postId of validIds) {
          // 获取关联的标签
          const associatedTags = await executeQuery(
            'SELECT tag_id FROM post_tags WHERE post_id = ?',
            [postId]
          );

          // 减少标签使用次数
          for (const tag of associatedTags) {
            await executeQuery(
              'UPDATE post_tag_definitions SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = ?',
              [tag.tag_id]
            );
          }

          // 删除推文标签关联
          await executeQuery('DELETE FROM post_tags WHERE post_id = ?', [postId]);
        }

        // 批量删除推文
        const deleteResult = await executeQuery(
          `DELETE FROM posts WHERE id IN (${placeholders})`,
          validIds
        );
        affectedCount = deleteResult.affectedRows;

      } else {
        // 批量状态更新操作
        let newStatus;
        let isOnline;
        let publishDate = null;

        switch (action) {
          case 'publish':
            newStatus = 'published';
            isOnline = 1;
            publishDate = new Date().toISOString().slice(0, 19).replace('T', ' ');
            break;
          case 'unpublish':
            newStatus = 'draft';
            isOnline = 0;
            break;
          case 'archive':
            newStatus = 'archived';
            isOnline = 0;
            break;
        }

        const updateResult = await executeQuery(
          `UPDATE posts 
           SET status = ?, is_online = ?, publish_date = ?, updated_at = NOW()
           WHERE id IN (${placeholders})`,
          [newStatus, isOnline, publishDate, ...validIds]
        );
        affectedCount = updateResult.affectedRows;
      }

      // 提交事务
      await executeQuery('COMMIT');

      // 记录操作日志
      const postTitles = existingPosts.map(post => post.title).join(', ');
      // await logAdminAction(admin.id, `BATCH_${action.toUpperCase()}_POST`, `批量${actionText}推文: ${postTitles}`, { ids: validIds });

      return {
        success: true,
        message: `批量${actionText}成功，共处理 ${affectedCount} 条推文`,
        data: {
          action,
          affectedCount,
          processedIds: validIds
        }
      };

    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }

  } catch (error: any) {
    console.error('批量操作推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '批量操作推文失败'
    });
  }
});
