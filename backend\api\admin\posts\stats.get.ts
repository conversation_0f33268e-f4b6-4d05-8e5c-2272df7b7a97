import { query } from '~/utils/database'

/**
 * 获取推文统计数据接口
 * GET /api/admin/posts/stats
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看统计数据'
    //   });
    // }

    // 获取推文总数统计
    const totalStats = await query(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived,
        SUM(view_count) as totalViews
      FROM posts
    `);

    // 获取今日阅读量
    const todayViews = await query(`
      SELECT COALESCE(SUM(view_count), 0) as todayViews
      FROM posts
      WHERE DATE(created_at) = CURDATE()
    `);

    const stats = totalStats[0];
    const todayViewsCount = todayViews[0]?.todayViews || 0;

    return {
      data: {
        total: stats.total || 0,
        published: stats.published || 0,
        draft: stats.draft || 0,
        pending: stats.pending || 0,
        archived: stats.archived || 0,
        totalViews: stats.totalViews || 0,
        todayViews: todayViewsCount
      }
    };

  } catch (error: any) {
    console.error('获取推文统计数据失败:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '获取推文统计数据失败'
    });
  }
});
