import { query } from '~/utils/database'

/**
 * 获取推文标签列表接口
 * GET /api/admin/posts/tags
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看标签列表'
    //   });
    // }

    // 查询所有标签
    const tags = await query(`
      SELECT
        id,
        name,
        usage_count,
        created_at
      FROM post_tag_definitions
      ORDER BY usage_count DESC, name ASC
    `);

    // 处理标签数据
    const processedTags = tags.map((tag: any) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count || 0,
      createdAt: tag.created_at
    }));

    return {
      data: processedTags
    };

  } catch (error: any) {
    console.error('获取标签列表失败:', error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || '获取标签列表失败'
    });
  }
});
