import { requestClient } from '#/api/request';
import type { PostManagementApi } from './types';

// ==================== 推文管理 API ====================

/**
 * 获取推文列表
 */
export async function getPostList(params: PostManagementApi.PostListParams = {}) {
  return requestClient.get<PostManagementApi.PostListResponse>('/admin/posts', {
    params,
  });
}

/**
 * 获取推文详情
 */
export async function getPostDetail(id: number) {
  return requestClient.get<PostManagementApi.PostDetailResponse>(`/admin/posts/${id}`);
}

/**
 * 创建推文
 */
export async function createPost(data: PostManagementApi.CreatePostParams) {
  return requestClient.post<PostManagementApi.OperationResponse>('/admin/posts', data);
}

/**
 * 更新推文
 */
export async function updatePost(id: number, data: PostManagementApi.UpdatePostParams) {
  return requestClient.put<PostManagementApi.OperationResponse>(`/admin/posts/${id}`, data);
}

/**
 * 删除推文
 */
export async function deletePost(id: number) {
  return requestClient.delete<PostManagementApi.OperationResponse>(`/admin/posts/${id}`);
}

/**
 * 发布/下线推文
 */
export async function publishPost(id: number, data: PostManagementApi.PublishPostParams) {
  return requestClient.post<PostManagementApi.OperationResponse>(`/admin/posts/${id}/publish`, data);
}

/**
 * 批量操作推文
 */
export async function batchOperatePost(data: PostManagementApi.BatchOperatePostParams) {
  return requestClient.post<PostManagementApi.OperationResponse>('/admin/posts/batch-operate', data);
}

// ==================== 标签管理 API ====================

/**
 * 获取所有标签
 */
export async function getAllTags() {
  return requestClient.get<{ data: PostManagementApi.PostTag[] }>('/admin/posts/tags');
}

/**
 * 创建标签
 */
export async function createTag(name: string) {
  return requestClient.post<PostManagementApi.OperationResponse>('/admin/posts/tags', { name });
}

/**
 * 删除标签
 */
export async function deleteTag(id: number) {
  return requestClient.delete<PostManagementApi.OperationResponse>(`/admin/posts/tags/${id}`);
}

// ==================== 统计数据 API ====================

/**
 * 获取推文统计数据
 */
export async function getPostStats() {
  return requestClient.get<{ data: PostManagementApi.PostStats }>('/admin/posts/stats');
}

// ==================== 公开接口（用于前台预览） ====================

/**
 * 获取公开推文列表（用于前台预览）
 */
export async function getPublicPostList(params: any = {}) {
  return requestClient.get<any>('/public/posts', {
    params,
  });
}
