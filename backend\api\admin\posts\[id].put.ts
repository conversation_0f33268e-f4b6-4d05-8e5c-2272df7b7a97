import { readBody, getRouterParam } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员更新推文接口
 * PUT /api/admin/posts/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法更新推文'
    //   });
    // }

    // 获取推文ID
    const postId = getRouterParam(event, 'id');
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的推文ID'
      });
    }

    // 检查推文是否存在
    const existingPost = await query(
      'SELECT * FROM posts WHERE id = ?',
      [postId]
    );

    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '推文不存在'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const {
      title,
      slug,
      content,
      author,
      status,
      isOnline,
      publishDate,
      tags
    } = body;

    // 验证slug唯一性（排除当前推文）
    if (slug && slug !== existingPost[0].slug) {
      const duplicatePost = await query(
        'SELECT id FROM posts WHERE slug = ? AND id != ?',
        [slug, postId]
      );

      if (duplicatePost.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '唯一标识已存在，请使用其他标识'
        });
      }
    }

    // 验证状态值
    if (status) {
      const validStatuses = ['draft', 'pending', 'published', 'archived'];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的状态值'
        });
      }
    }

    // 开始事务
    await query('START TRANSACTION');

    try {
      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (title !== undefined) {
        updateFields.push('title = ?');
        updateValues.push(title);
      }
      if (slug !== undefined) {
        updateFields.push('slug = ?');
        updateValues.push(slug);
      }
      if (content !== undefined) {
        updateFields.push('content = ?');
        updateValues.push(content);
      }
      if (author !== undefined) {
        updateFields.push('author = ?');
        updateValues.push(author);
      }
      if (status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(status);
      }
      if (isOnline !== undefined) {
        updateFields.push('is_online = ?');
        updateValues.push(isOnline ? 1 : 0);
      }
      if (publishDate !== undefined) {
        updateFields.push('publish_date = ?');
        updateValues.push(publishDate);
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      updateValues.push(postId);

      // 更新推文
      if (updateFields.length > 1) { // 除了updated_at还有其他字段
        const updateQuery = `UPDATE posts SET ${updateFields.join(', ')} WHERE id = ?`;
        await query(updateQuery, updateValues);
      }

      // 处理标签更新
      if (tags !== undefined) {
        // 删除现有标签关联
        const oldTags = await query(
          'SELECT tag_id FROM post_tags WHERE post_id = ?',
          [postId]
        );

        // 减少旧标签的使用次数
        for (const oldTag of oldTags) {
          await query(
            'UPDATE post_tag_definitions SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = ?',
            [oldTag.tag_id]
          );
        }

        // 删除旧的标签关联
        await query('DELETE FROM post_tags WHERE post_id = ?', [postId]);

        // 添加新标签
        if (tags && tags.length > 0) {
          for (const tagName of tags) {
            if (!tagName.trim()) continue;

            // 查找或创建标签
            let tagResult = await query(
              'SELECT id FROM post_tag_definitions WHERE name = ?',
              [tagName.trim()]
            );

            let tagId;
            if (tagResult.length === 0) {
              // 创建新标签
              const createTagResult = await query(
                'INSERT INTO post_tag_definitions (name, usage_count, created_at) VALUES (?, 1, NOW())',
                [tagName.trim()]
              );
              tagId = createTagResult.insertId;
            } else {
              tagId = tagResult[0].id;
              // 更新使用次数
              await query(
                'UPDATE post_tag_definitions SET usage_count = usage_count + 1 WHERE id = ?',
                [tagId]
              );
            }

            // 关联推文和标签
            await query(
              'INSERT INTO post_tags (post_id, tag_id) VALUES (?, ?)',
              [postId, tagId]
            );
          }
        }
      }

      // 提交事务
      await query('COMMIT');

      // 记录操作日志
      // await logAdminAction(admin.id, 'UPDATE_POST', `更新推文: ${title || existingPost[0].title}`, { postId });

      return {
        success: true,
        message: '推文更新成功',
        data: {
          id: postId
        }
      };

    } catch (error) {
      // 回滚事务
      await query('ROLLBACK');
      throw error;
    }

  } catch (error: any) {
    console.error('更新推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '更新推文失败'
    });
  }
});
