import { readBody, getRouterParam } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员更新推文接口
 * PUT /api/admin/posts/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法更新推文'
    //   });
    // }

    // 获取推文ID
    const postId = getRouterParam(event, 'id');
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的推文ID'
      });
    }

    // 检查推文是否存在
    const existingPost = await query(
      'SELECT * FROM posts WHERE id = ?',
      [postId]
    );

    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '推文不存在'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const {
      title,
      slug,
      content,
      author,
      status,
      isOnline,
      publishDate
    } = body;

    // 验证slug唯一性（排除当前推文）
    if (slug && slug !== existingPost[0].slug) {
      const duplicatePost = await query(
        'SELECT id FROM posts WHERE slug = ? AND id != ?',
        [slug, postId]
      );

      if (duplicatePost.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '唯一标识已存在，请使用其他标识'
        });
      }
    }

    // 验证状态值
    if (status) {
      const validStatuses = ['draft', 'pending', 'published', 'archived'];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的状态值'
        });
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (title !== undefined) {
      updateFields.push('title = ?');
      updateValues.push(title);
    }
    if (slug !== undefined) {
      updateFields.push('slug = ?');
      updateValues.push(slug);
    }
    if (content !== undefined) {
      updateFields.push('content = ?');
      updateValues.push(content);
    }
    if (author !== undefined) {
      updateFields.push('author = ?');
      updateValues.push(author);
    }
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (isOnline !== undefined) {
      updateFields.push('is_online = ?');
      updateValues.push(isOnline ? 1 : 0);
    }
    if (publishDate !== undefined) {
      updateFields.push('publish_date = ?');
      updateValues.push(publishDate);
    }

    // 添加更新时间
    updateFields.push('updated_at = NOW()');
    updateValues.push(postId);

    // 更新推文
    if (updateFields.length > 1) { // 除了updated_at还有其他字段
      const updateQuery = `UPDATE posts SET ${updateFields.join(', ')} WHERE id = ?`;
      await query(updateQuery, updateValues);
    }

    // 记录操作日志
    // await logAdminAction(admin.id, 'UPDATE_POST', `更新推文: ${title || existingPost[0].title}`, { postId });

    return {
      success: true,
      message: '推文更新成功',
      data: {
        id: postId
      }
    };

  } catch (error: any) {
    console.error('更新推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '更新推文失败'
    });
  }
});
