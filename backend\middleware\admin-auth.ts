/**
 * 管理员认证中间件
 * 用于保护需要管理员权限的API路由
 */
export default defineEventHandler(async (event) => {
  // 只处理管理员API路由
  const url = event.node.req.url || '';
  if (!url.startsWith('/api/admin/') &&
      !url.startsWith('/api/auth/admin/')) {
    return;
  }

  // 跳过登录和登出接口，以及短剧管理接口（临时）
  if (url === '/api/auth/admin/login' ||
      url === '/api/auth/admin/logout' ||
      url.startsWith('/api/admin/dramas') ||
      url.startsWith('/api/admin/talent-management') ||
      url.startsWith('/api/admin/brand-management') ||
      url.startsWith('/api/admin/news') ||
      url.startsWith('/api/admin/banners') ||
      url.startsWith('/api/admin/content-management/tags') ||
      url.startsWith('/api/admin/content-management/platforms') ||
      url.startsWith('/api/admin/recharge-records') ||
      url.startsWith('/api/admin/posts')) {
    return;
  }

  try {
    // 验证管理员令牌
    const adminPayload = verifyAdminAccessToken(event);
    
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '管理员认证失败，请重新登录'
      });
    }

    // 验证管理员状态
    const admin = await findAdminById(adminPayload.id);
    if (!admin || admin.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: '管理员账户已被禁用'
      });
    }

    // 将管理员信息添加到事件上下文
    event.context.admin = adminPayload;
    event.context.adminInfo = admin;

  } catch (error: any) {
    logger.warn('管理员认证失败', {
      url: event.node.req.url,
      error: error.message,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 401,
      statusMessage: '认证失败'
    });
  }
});
