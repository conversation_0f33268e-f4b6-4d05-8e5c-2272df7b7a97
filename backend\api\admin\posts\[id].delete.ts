import { getRouterParam } from 'h3'
import { query } from '~/utils/database'

/**
 * 管理员删除推文接口
 * DELETE /api/admin/posts/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息（临时跳过认证检查）
    const admin = event.context.admin || { id: 1, username: 'test' };

    // 临时跳过权限检查
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法删除推文'
    //   });
    // }

    // 获取推文ID
    const postId = getRouterParam(event, 'id');
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的推文ID'
      });
    }

    // 检查推文是否存在
    const existingPost = await query(
      'SELECT id, title FROM posts WHERE id = ?',
      [postId]
    );

    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '推文不存在'
      });
    }

    // 开始事务
    await query('START TRANSACTION');

    try {
      // 获取关联的标签
      const associatedTags = await query(
        'SELECT tag_id FROM post_tags WHERE post_id = ?',
        [postId]
      );

      // 减少标签使用次数
      for (const tag of associatedTags) {
        await query(
          'UPDATE post_tag_definitions SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = ?',
          [tag.tag_id]
        );
      }

      // 删除推文标签关联
      await query('DELETE FROM post_tags WHERE post_id = ?', [postId]);

      // 删除推文
      await query('DELETE FROM posts WHERE id = ?', [postId]);

      // 提交事务
      await query('COMMIT');

      // 记录操作日志
      // await logAdminAction(admin.id, 'DELETE_POST', `删除推文: ${existingPost[0].title}`, { postId });

      return {
        success: true,
        message: '推文删除成功',
        data: {
          id: postId
        }
      };

    } catch (error) {
      // 回滚事务
      await query('ROLLBACK');
      throw error;
    }

  } catch (error: any) {
    console.error('删除推文失败:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || '删除推文失败'
    });
  }
});
